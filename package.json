{"name": "epay-cardholder-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "npm run prisma:generate && next build", "start": "next start", "lint": "next lint", "start:prod": "prisma migrate deploy && npm start", "prisma:generate": "prisma generate", "test": "vitest"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.3", "better-auth": "^1.2.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "next": "15.3.2", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-phone-number-input": "^3.4.12", "tailwind-merge": "^3.3.0", "twilio": "^5.7.0", "zod": "^3.25.30"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5", "vitest": "^3.2.1"}}