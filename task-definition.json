{"family": "<TASK_DEFINITION_FAMILY>", "taskRoleArn": "arn:aws:iam::<AWS_ACCOUNT>:role/<TASK_ROLE>", "executionRoleArn": "arn:aws:iam::<AWS_ACCOUNT>:role/<EXECUTION_ROLE>", "containerDefinitions": [{"name": "<CONTAINER_NAME>", "image": "<ECR_REPOSITORY_URL>", "cpu": 512, "memory": 2048, "essential": true, "portMappings": [{"name": "<ENV>-portal-8000-tcp", "containerPort": 8000, "hostPort": 8000, "protocol": "tcp", "appProtocol": "http"}], "secrets": [{"name": "JSON_SECRETS", "valueFrom": "<ARN_JSON_SECRETS>"}, {"name": "DATABASE_URL", "valueFrom": "arn:aws:ssm:ap-southeast-2:<AWS_ACCOUNT>:parameter/<ENV_SECRET>/<APP_NAME>/DATABASE_URL"}], "environment": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/<ENV>-<APP_NAME>-definition", "awslogs-region": "ap-southeast-2", "awslogs-stream-prefix": "ecs"}}}], "requiresCompatibilities": ["FARGATE"], "networkMode": "awsvpc", "cpu": "1024", "memory": "3GB"}