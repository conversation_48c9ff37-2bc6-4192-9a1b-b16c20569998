-- CreateTable
CREATE TABLE "Card" (
    "id" TEXT NOT NULL,
    "crn" TEXT NOT NULL,
    "isActivated" BOOLEAN NOT NULL DEFAULT false,
    "isLocked" BOOLEAN NOT NULL DEFAULT false,
    "isAdminLocked" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "cardholderId" TEXT NOT NULL,

    CONSTRAINT "Card_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Card_crn_idx" ON "Card"("crn");

-- AddForeignKey
ALTER TABLE "Card" ADD CONSTRAINT "Card_cardholderId_fkey" FOREIGN KEY ("cardholderId") REFERENCES "Cardholder"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
