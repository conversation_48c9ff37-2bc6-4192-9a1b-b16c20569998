-- AlterTable
ALTER TABLE "Cardholder" ADD COLUMN     "phone" TEXT,
ADD COLUMN     "phoneVerified" BOOLEAN;

-- CreateTable
CREATE TABLE "OneTimePassword" (
    "cardholderId" TEXT NOT NULL,
    "phoneNumber" TEXT NOT NULL,
    "attemptCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "OneTimePassword_pkey" PRIMARY KEY ("cardholderId")
);
