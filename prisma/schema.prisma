// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

/**
 * Auth Models*
 */
model Account {
  id                 String    @id @default(uuid())
  compoundId         String    @unique
  providerType       String
  providerId         String
  refreshToken       String?
  accessToken        String?
  accessTokenExpires DateTime?
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @default(now())

  accountId             String
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?

  cardholderId String
  cardholder   Cardholder @relation(fields: [cardholderId], references: [id])

  @@index([providerId], name: "providerId")
  @@index([cardholderId], name: "cardholderId")
}

model Session {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
  expiresAt DateTime
  token     String
  ipAddress String?
  userAgent String?

  cardholderId String
  cardholder   Cardholder @relation(fields: [cardholderId], references: [id])

  @@unique([token])
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?
}

model Cardholder {
  id String @id @default(uuid())

  firstName     String?
  lastName      String?
  fullName      String?
  email         String?   @unique
  emailVerified Boolean?
  phone         String?
  phoneVerified Boolean?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @default(now())
  sessions      Session[]
  accounts      Account[]
  cards         Card[]

  @@index([email])
}

/**
 * OTP Model *
 */
model OneTimePassword {
  cardholderId String   @id
  phoneNumber  String
  attemptCount Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @default(now())
}

/**
 * Application Models *
 */
model Token {
  id        String   @id @default(uuid())
  crn       String   @unique
  email     String
  usedCount Int      @default(0)
  expires   DateTime
}

enum CardType {
  PHYSICAL
  VIRTUAL
}

model Card {
  id            String   @id @default(uuid())
  crn           String
  type          CardType
  isActivated   Boolean  @default(false)
  isLocked      Boolean  @default(false)
  isAdminLocked Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @default(now())

  cardholderId String
  cardholder   Cardholder @relation(fields: [cardholderId], references: [id])

  @@index([crn])
}
