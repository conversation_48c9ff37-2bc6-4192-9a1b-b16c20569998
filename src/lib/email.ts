import config from '@/config/server';
const isDev = config.env.isLocal || config.env.isDev;

export const templates = {
  magicLink: isDev ? 40149946 : 40149946,
  otp: isDev ? 40215220 : 40215220,
};

const getPostmarkHeaders = () => ({
  Accept: 'application/json',
  'Content-Type': 'application/json',
  'X-Postmark-Server-Token': config.postmark.apiKey,
});

export const sendEmailTemplate = async (
  toEmail: string,
  templateId: number,
  templateModel: Record<string, string>,
) => {
  return fetch('https://api.postmarkapp.com/email/withTemplate', {
    method: 'POST',
    headers: getPostmarkHeaders(),
    body: JSON.stringify({
      To: toEmail,
      From: config.postmark.fromEmail,
      TemplateId: templateId,
      TemplateModel: templateModel,
    }),
  }).then((res) => res.json());
};
