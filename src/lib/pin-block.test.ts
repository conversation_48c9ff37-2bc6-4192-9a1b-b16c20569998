import { describe, it, expect } from 'vitest';
import { decryptPin, encryptPin } from './pin-block';

describe('Pin block', () => {
  const PIN = '1234';
  const CRN = '4567890123456789';
  const encryptionKey = 'E14E3F7260D42E35C6ED28711DC3FC95';

  it('should encrypt a pin', () => {
    const encrypted = encryptPin(PIN, CRN, encryptionKey);
    expect(encrypted).toBe('AEB98A40E1CE570A');
  });

  it('should decrypt a pin', () => {
    const decrypted = decryptPin('AEB98A40E1CE570A', CRN, encryptionKey);
    expect(decrypted).toBe(PIN);
  });
});
