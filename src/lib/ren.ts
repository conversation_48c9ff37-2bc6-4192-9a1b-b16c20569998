import config from '@/config/server';
import { encryptPin } from './pin-block';

export async function registerPhysicalCard(data: {
  crn: string;
  cvv: string;
  expiry: string;
  pin: string;
  lockCode: string;
}) {
  console.log('Registering physical card start', data);
  await sleep();

  try {
    // Encode pin code
    console.log('Encoding pin', data.pin, data.crn);
    const pinBlock = encryptPin(data.pin, data.crn, config.pinBlock.key);
    // Register Card
    console.log('Registering card');
    return {
      data: { success: true, pinBlock },
      error: null,
    };
  } catch (error) {
    console.error('Error registerCard', error);
    return {
      data: null,
      error,
    };
  }
}

export async function registerVirtualCard({ crn }: { crn: string }) {
  console.log('Registering virtual card start', { crn });
  await sleep();

  try {
    console.log('Registering card');
    return {
      data: { success: true },
      error: null,
    };
  } catch (error) {
    console.error('Error registerCard', error);
    return {
      data: null,
      error,
    };
  }
}

export async function validatePhysicalCard({
  crn,
  cvv,
  expiry,
}: {
  crn: string;
  cvv: string;
  expiry: string;
}) {
  await sleep();

  try {
    console.log('Validating physical card', crn, cvv, expiry);
    if (cvv !== '123') {
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error validatePhysicalCard', error);
    return false;
  }
}

export async function validatePhysicalLockCode(crn: string, lockCode: string) {
  await sleep();
  try {
    console.log('Validating lock code', crn, lockCode);
    return lockCode === '1234';
  } catch (error) {
    console.error('Error validatePhysicalLockCode', error);
    return false;
  }
}

// TODO: remove
function sleep(ms: number = 1000) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, ms);
  });
}
