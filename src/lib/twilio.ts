import twilio from 'twilio';
import config from '@/config/server';

const client = twilio(config.twilio.accountSid, config.twilio.authToken);

export async function sendVerification(phone: string, channel: 'sms' | 'whatsapp') {
  try {
    console.log('Sending OTP', phone, channel);
    const res = await client.verify.v2
      .services(config.twilio.verifyServiceSid)
      .verifications.create({ to: phone, channel });
    return {
      data: res.toJSON(),
      error: null,
    };
  } catch (ex: unknown) {
    console.error('sendVerification error:', ex);
    const errorMessage = ex instanceof Error ? ex.message : 'Unknown error';
    return {
      data: null,
      error: `Error sending OTP: ${errorMessage}`,
    };
  }
}

export async function checkVerification(phone: string, code: string) {
  try {
    console.log('Checking OTP', phone, code);
    const res = await client.verify.v2
      .services(config.twilio.verifyServiceSid)
      .verificationChecks.create({ to: phone, code });
    return {
      data: res.toJSON(),
      error: null,
    };
  } catch (ex: unknown) {
    console.error('checkVerification error:', ex);
    const errorMessage = ex instanceof Error ? ex.message : 'Unknown error';
    return {
      data: null,
      error: `Error verifying OTP: ${errorMessage}`,
    };
  }
}
