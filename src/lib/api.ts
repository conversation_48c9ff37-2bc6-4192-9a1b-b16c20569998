import { NextRequest, NextResponse } from 'next/server';
import config from '@/config/server';

/**
 * Validates the X-API-Key header against the configured API key
 * @param request The Next.js request object
 * @returns A NextResponse error or null if validation passes
 */
export function validateApiKey(request: NextRequest): NextResponse | null {
  const apiKey = request.headers.get('x-api-key');

  if (!apiKey) {
    return NextResponse.json({ error: 'Missing API key' }, { status: 401 });
  }

  if (apiKey !== config.external.epayPortalApiKey) {
    return NextResponse.json({ error: 'Invalid API key' }, { status: 403 });
  }

  return null;
}
