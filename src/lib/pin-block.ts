import crypto from 'crypto';

/**
 * Generates a PIN block using ANSI X9.8 format
 * @param pin - The customer PIN (string of digits)
 * @param cardNumber - The full card number including check digit
 * @returns The generated PIN block as a hexadecimal string
 */
export function generatePinBlock(pin: string, cardNumber: string): string {
  // Validate inputs
  if (!pin || pin.length < 4 || pin.length > 12) {
    throw new Error('PIN must be between 4 and 12 digits');
  }

  if (!cardNumber || cardNumber.length < 13) {
    throw new Error('Card number must be at least 13 digits');
  }

  // Remove any spaces or hyphens from card number
  const cleanCardNumber = cardNumber.replace(/[\s-]/g, '');

  // Step 1: Create first block - 0 + PIN length + PIN + padding with F
  const pinLength = pin.length.toString().padStart(2, '0');
  const pinBlock1 = '0' + pinLength[1] + pin.padEnd(14, 'F');

  // Step 2: Create second block - 0000 + 12 rightmost digits (excluding check digit)
  const cardDigitsOnly = cleanCardNumber.replace(/\D/g, ''); // Remove non-digits
  const cardWithoutCheckDigit = cardDigitsOnly.slice(0, -1); // Remove last digit (check digit)
  const last12Digits = cardWithoutCheckDigit.slice(-12).padStart(12, '0');
  const pinBlock2 = '0000' + last12Digits;

  // Step 3: XOR the two blocks
  const block1Bytes = Buffer.from(pinBlock1, 'hex');
  const block2Bytes = Buffer.from(pinBlock2, 'hex');
  const xorResult = Buffer.alloc(8);

  for (let i = 0; i < 8; i++) {
    xorResult[i] = block1Bytes[i] ^ block2Bytes[i];
  }

  return xorResult.toString('hex').toUpperCase();
}

/**
 * Encrypts a PIN block using Triple DES (des-ede3)
 * @param pinBlock - The PIN block to encrypt (hex string)
 * @param key - The 32-character hex key for Triple DES
 * @returns The encrypted PIN block as a hexadecimal string
 */
export function encryptPinBlock(pinBlock: string, key: string): string {
  if (pinBlock.length !== 16) {
    throw new Error('PIN block must be 16 hex characters (8 bytes)');
  }

  if (key.length !== 32) {
    throw new Error('Key must be 32 hex characters (16 bytes)');
  }

  // For Triple DES, we need to create a 24-byte key from our 16-byte key
  // Standard approach: K1 + K2 + K1 (where K1 and K2 are 8 bytes each)
  const key1 = key.substring(0, 16); // First 8 bytes
  const key2 = key.substring(16, 32); // Second 8 bytes
  const tripleDesKey = key1 + key2 + key1; // 24 bytes total

  // Convert hex strings to buffers
  const dataBuffer = Buffer.from(pinBlock, 'hex');
  const keyBuffer = Buffer.from(tripleDesKey, 'hex');

  // Use des-ede3 which is the proper Triple DES implementation
  const cipher = crypto.createCipheriv('des-ede3', keyBuffer, null);
  cipher.setAutoPadding(false);

  const encrypted = Buffer.concat([cipher.update(dataBuffer), cipher.final()]);

  return encrypted.toString('hex').toUpperCase();
}

/**
 * Decrypts a PIN block using Triple DES (des-ede3)
 * @param encryptedPinBlock - The encrypted PIN block (hex string)
 * @param key - The 32-character hex key for Triple DES
 * @returns The decrypted PIN block as a hexadecimal string
 */
export function decryptPinBlock(encryptedPinBlock: string, key: string): string {
  if (encryptedPinBlock.length !== 16) {
    throw new Error('Encrypted PIN block must be 16 hex characters (8 bytes)');
  }

  if (key.length !== 32) {
    throw new Error('Key must be 32 hex characters (16 bytes)');
  }

  // For Triple DES, we need to create a 24-byte key from our 16-byte key
  // Standard approach: K1 + K2 + K1 (where K1 and K2 are 8 bytes each)
  const key1 = key.substring(0, 16); // First 8 bytes
  const key2 = key.substring(16, 32); // Second 8 bytes
  const tripleDesKey = key1 + key2 + key1; // 24 bytes total

  // Convert hex strings to buffers
  const dataBuffer = Buffer.from(encryptedPinBlock, 'hex');
  const keyBuffer = Buffer.from(tripleDesKey, 'hex');

  // Use des-ede3 which is the proper Triple DES implementation
  const decipher = crypto.createDecipheriv('des-ede3', keyBuffer, null);
  decipher.setAutoPadding(false);

  const decrypted = Buffer.concat([decipher.update(dataBuffer), decipher.final()]);

  return decrypted.toString('hex').toUpperCase();
}

/**
 * Extracts the PIN from a decrypted PIN block by reversing the ANSI X9.8 process
 * @param pinBlock - The decrypted PIN block (hex string)
 * @param cardNumber - The card number used in the original PIN block generation
 * @returns The extracted PIN as a string
 */
export function extractPinFromBlock(pinBlock: string, cardNumber: string): string {
  if (pinBlock.length !== 16) {
    throw new Error('PIN block must be 16 hex characters');
  }

  if (!cardNumber || cardNumber.length < 13) {
    throw new Error('Card number must be at least 13 digits');
  }

  // Remove any spaces or hyphens from card number
  const cleanCardNumber = cardNumber.replace(/[\s-]/g, '');

  // Recreate the second block used in the original XOR operation
  const cardDigitsOnly = cleanCardNumber.replace(/\D/g, '');
  const cardWithoutCheckDigit = cardDigitsOnly.slice(0, -1);
  const last12Digits = cardWithoutCheckDigit.slice(-12).padStart(12, '0');
  const pinBlock2 = '0000' + last12Digits;

  // XOR the decrypted PIN block with the card-based block to get the original PIN block
  const decryptedBytes = Buffer.from(pinBlock, 'hex');
  const block2Bytes = Buffer.from(pinBlock2, 'hex');
  const originalBlock = Buffer.alloc(8);

  for (let i = 0; i < 8; i++) {
    originalBlock[i] = decryptedBytes[i] ^ block2Bytes[i];
  }

  const originalBlockHex = originalBlock.toString('hex').toUpperCase();

  // Now extract the PIN from the original format
  // First character should be 0
  if (originalBlockHex[0] !== '0') {
    throw new Error('Invalid PIN block format after decryption');
  }

  // Second character is the PIN length
  const pinLength = parseInt(originalBlockHex[1], 16);
  if (pinLength < 4 || pinLength > 12) {
    throw new Error('Invalid PIN length in decrypted block');
  }

  // Extract the PIN
  const pin = originalBlockHex.substring(2, 2 + pinLength);

  // Validate that PIN contains only digits
  if (!/^\d+$/.test(pin)) {
    throw new Error('Invalid PIN format in decrypted block');
  }

  return pin;
}

/**
 * Complete function to encrypt a PIN using ANSI X9.8 format
 * @param pin - The customer PIN
 * @param cardNumber - The card number
 * @param key - The encryption key (32 hex characters)
 * @returns The encrypted PIN block
 */
export function encryptPin(pin: string, cardNumber: string, key: string): string {
  const pinBlock = generatePinBlock(pin, cardNumber);
  return encryptPinBlock(pinBlock, key);
}

/**
 * Complete function to decrypt a PIN using ANSI X9.8 format
 * @param encryptedPinBlock - The encrypted PIN block
 * @param cardNumber - The card number used in the original PIN block generation
 * @param key - The decryption key (32 hex characters)
 * @returns The decrypted PIN
 */
export function decryptPin(encryptedPinBlock: string, cardNumber: string, key: string): string {
  const decryptedBlock = decryptPinBlock(encryptedPinBlock, key);
  return extractPinFromBlock(decryptedBlock, cardNumber);
}
