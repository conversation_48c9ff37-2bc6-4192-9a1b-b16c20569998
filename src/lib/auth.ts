import { headers } from 'next/headers';
import { betterAuth } from 'better-auth';
import { APIError, createAuthMiddleware } from 'better-auth/api';
import { prismaAdapter } from 'better-auth/adapters/prisma';
import { emailOTP, magicLink } from 'better-auth/plugins';
import config from '@/config/server';
import { sendEmailTemplate, templates } from './email';
import { prisma } from './prisma';
import { redirect } from 'next/navigation';

export const auth = betterAuth({
  baseURL: config.auth.baseUrl,
  secret: config.auth.secret,
  ...getAuthDbConfig(),
  ...getAuthPluginConfig(),
  ...getAuthHooksConfig(),
});

function getAuthDbConfig() {
  return {
    database: prismaAdapter(prisma, {
      provider: 'postgresql',
    }),
    account: {
      fields: {
        userId: 'cardholderId',
      },
    },
    session: {
      expiresIn: 60 * 60 * 24 * 1, // 1 day
      fields: {
        userId: 'cardholderId',
      },
    },
    user: {
      modelName: 'cardholder',
      fields: {
        name: 'fullName',
      },
    },
  };
}

function getAuthPluginConfig() {
  return {
    plugins: [
      magicLink({
        expiresIn: config.auth.magicLinkExpiresIn,
        sendMagicLink: async ({ email, url }) => {
          console.log('Sending magic link', email);
          return sendEmailTemplate(email, templates.magicLink, {
            url,
            expiry: '1 minute',
          });
        },
      }),

      emailOTP({
        expiresIn: config.auth.magicLinkExpiresIn,
        async sendVerificationOTP({ email, otp }) {
          console.log('Sending OTP', email);
          return sendEmailTemplate(email, templates.otp, {
            otp,
            expiry: '1 minute',
          });
        },
      }),
    ],
  };
}

function getAuthHooksConfig() {
  return {
    hooks: {
      after: createAuthMiddleware(async (ctx) => {
        if (
          ctx.path.startsWith('/magic-link/verify') ||
          ctx.path.startsWith('/sign-in/email-otp')
        ) {
          if (ctx.context.newSession) {
            // Similar function as `revokeOtherSessions`
            // but there is no way to call the auth api from the hook
            const session = ctx.context.newSession;
            const sessions = await ctx.context.internalAdapter.listSessions(session.user.id);
            const activeSessions = sessions.filter((session) => {
              return session.expiresAt > new Date();
            });
            const otherSessions = activeSessions.filter((s) => s.token !== session.session.token);
            await Promise.all(
              otherSessions.map((session) =>
                ctx.context.internalAdapter.deleteSession(session.token),
              ),
            );
            return ctx.context.returned;
          }
        }
      }),
    },
  };
}

export const handleApiError = (error: unknown) => {
  if (error instanceof APIError) {
    return {
      data: null,
      error: {
        ...error.body,
        status: error.statusCode,
        statusText: error.status,
      },
    };
  }
  return {
    data: null,
    error: {
      message: 'An unknown error occurred',
      status: 500,
      statusText: 'BAD_REQUEST',
      code: 'UNKNOWN_ERROR',
    },
  };
};

export async function getCurrentSession() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session) {
    redirect('/login');
  }

  return session;
}
