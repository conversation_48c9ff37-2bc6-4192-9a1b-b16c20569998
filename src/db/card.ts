import { CardType } from '@prisma/client';
import { prisma } from '@/lib/prisma';

export function createCard({
  crn,
  cardholderId,
  type,
}: {
  crn: string;
  cardholderId: string;
  type: CardType;
}) {
  return prisma.card.create({
    data: {
      crn,
      cardholderId,
      type,
    },
  });
}

export function getCardsByCardholderId(cardholderId: string) {
  return prisma.card.findMany({
    where: { cardholderId },
  });
}

export function getCardById(id: string) {
  return prisma.card.findUnique({
    where: { id },
  });
}
