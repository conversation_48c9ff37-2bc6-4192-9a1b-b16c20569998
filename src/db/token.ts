'server only';

import { prisma } from '@/lib/prisma';

const TOKEN_EXPIRY = 1000 * 60 * 60 * 24; // 24 hours

export function createToken(crn: string, email: string) {
  return prisma.token.create({
    data: {
      crn,
      email,
      expires: new Date(Date.now() + TOKEN_EXPIRY),
    },
  });
}

export function getTokenById(id: string) {
  return prisma.token.findUnique({
    where: { id },
  });
}

export function invalidateToken(id: string) {
  return prisma.token.update({
    where: { id },
    data: {
      usedCount: { increment: 1 },
    },
  });
}
