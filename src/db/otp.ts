'server only';

import { prisma } from '@/lib/prisma';

export function upsertOtpForCardholder(cardholderId: string, phoneNumber: string) {
  return prisma.oneTimePassword.upsert({
    where: { cardholderId },
    create: {
      cardholderId,
      phoneNumber,
    },
    update: {
      phoneNumber,
      attemptCount: 0,
    },
  });
}

export function getOtpForCardholder(cardholderId: string) {
  return prisma.oneTimePassword.findUnique({
    where: { cardholderId },
  });
}

export function incrementOtpAttemptCount(cardholderId: string) {
  return prisma.oneTimePassword.update({
    where: { cardholderId },
    data: {
      attemptCount: { increment: 1 },
    },
  });
}
