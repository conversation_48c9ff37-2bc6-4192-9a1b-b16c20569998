import { NextRequest, NextResponse } from 'next/server';
import z from 'zod';
import config from '@/config/server';
import { createToken } from '@/db/token';
import { validateApiKey } from '@/lib/api';

const schema = z.object({
  crn: z.string().regex(/^\d+$/).min(10, { message: 'Invalid card number' }),
  email: z.string().email({ message: 'Invalid email' }),
});

export async function POST(request: NextRequest) {
  const validationError = validateApiKey(request);

  if (validationError) {
    return validationError;
  }

  const res = await request.json();
  const validation = schema.safeParse(res);

  if (!validation.success) {
    return NextResponse.json({ error: validation.error.flatten() }, { status: 400 });
  }

  try {
    const data = await createToken(validation.data.crn, validation.data.email);
    const registrationUrl = `${config.auth.baseUrl}/register?token=${data.id}`;
    return NextResponse.json({ registrationUrl });
  } catch (error) {
    console.error('createToken error:', error);
    return NextResponse.json({ error: 'Failed to create registration URL' }, { status: 500 });
  }
}
