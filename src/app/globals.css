@import 'tailwindcss';

:root {
  /* Shadcn */
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: #ea3829;
  --border: oklch(0.922 0 0);
  --input: white;
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

  /* Custom */
  --background: #ffffff;
  --foreground: #171717;
  --primary-gradient:
    linear-gradient(
      225deg,
      rgba(255, 115, 0, 0.7) 6.43%,
      rgba(255, 115, 0, 0) 63.21%,
      rgba(255, 115, 0, 0) 75%
    ),
    #ff3232;
}

@theme inline {
  /* Shadcn */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Custom */
  --animate-caret-blink: caret-blink 1s ease-in-out infinite;
  @keyframes caret-blink {
    0%,
    70%,
    100% {
      opacity: 1;
    }
    20%,
    50% {
      opacity: 0;
    }
  }

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-error: #ea3829;

  --color-neutral-50: #ebebeb;
  --color-neutral-100: #f6f6f8;
  --color-neutral-200: #e5e7eb;
  --color-neutral-700: #49494c;
  --color-neutral-800: #171717;

  --color-primary-500: #ff3232;

  --color-secondary-500: #ff7300;
  --color-secondary-600: #e46f00;
}

@layer components {
  .gradient-primary {
    background: var(--primary-gradient);
  }

  .title {
    @apply text-4xl font-medium text-neutral-800;
  }

  .heading-xl {
    @apply font-bold text-neutral-800;
    font-size: 2rem;
  }

  .heading-l {
    @apply text-2xl font-bold text-neutral-800;
  }

  .heading-m {
    @apply text-xl font-bold text-neutral-800;
  }

  .body-l {
    @apply text-base text-neutral-700;
  }
}

body {
  background: var(--color-neutral-50);
  color: var(--foreground);
  font-family: var(--geograph), Arial, Helvetica, sans-serif;
}
