import { WelcomeLayoutError } from '@/components/welcome-layout';
import { getTokenById } from '@/db/token';
import { AuthErrorCode } from '@/types/errors';
import { LoginForm } from './login-form';

export default async function LoginPage({
  searchParams: searchParamsPromise,
}: {
  searchParams: Promise<{
    redirectTo?: string;
    error?: string;
    token?: string;
  }>;
}) {
  const { redirectTo, error, token } = await searchParamsPromise;

  if (error) {
    return <WelcomeLayoutError error={error as AuthErrorCode} backHref="/login" />;
  }

  let email = '';

  if (token) {
    try {
      const data = await getTokenById(token);
      if (data) {
        email = data.email;
      }
    } catch (error) {
      console.error('Silent error:', error);
    }
  }

  return <LoginForm email={email} redirectTo={redirectTo} />;
}
