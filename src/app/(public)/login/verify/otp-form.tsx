'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { ArrowLeftIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { InputOTP, InputOTPSlot } from '@/components/ui/input-otp';
import { authClient } from '@/lib/auth-client';
import { sendEmailOTP } from '../actions';

const otpSchema = z.object({
  otp: z.string().min(6, {
    message: 'Your code must be 6 characters.',
  }),
});

export function OtpForm({ email, redirectTo = '/' }: { email: string; redirectTo?: string }) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const form = useForm({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      otp: '',
    },
  });

  const onSubmit = async (data: z.infer<typeof otpSchema>) => {
    const { error: resError } = await authClient.signIn.emailOtp({
      email,
      otp: data.otp,
    });

    if (resError) {
      console.error('OTP Verification Error', resError);
      form.setError('otp', { message: getErrorMessage(resError.code) });
    } else {
      router.push(redirectTo);
    }
  };

  const handleResend = async () => {
    setLoading(true);
    await sendEmailOTP(email);
    setLoading(false);
  };

  return (
    <div className="md:max-w-[456px] text-neutral-700 space-y-6">
      <h1 className="title">Verify your email</h1>
      <p>
        Enter the 6-digit code we sent to <b>{email}</b>.
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 mt-12">
          <FormField
            control={form.control}
            name="otp"
            render={({ field }) => (
              <FormItem className="w-fit">
                <FormLabel className="flex items-center justify-between">
                  <span className="text-base">Enter the code</span>

                  <Button
                    type="button"
                    variant="link"
                    size="sm"
                    className="p-0 h-auto text-base font-bold text-primary-500 underline"
                    disabled={loading}
                    onClick={handleResend}
                  >
                    Resend
                  </Button>
                </FormLabel>
                <FormControl>
                  <InputOTP maxLength={6} {...field} pattern="\d*">
                    <InputOTPSlot index={0} />
                    <InputOTPSlot index={1} />
                    <InputOTPSlot index={2} />
                    <InputOTPSlot index={3} />
                    <InputOTPSlot index={4} />
                    <InputOTPSlot index={5} />
                  </InputOTP>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-between mt-16">
            <Button
              onClick={() => router.back()}
              variant="link"
              className="p-0! h-auto text-base font-bold text-secondary-500"
            >
              <ArrowLeftIcon className="size-5" /> Back
            </Button>
            <Button
              disabled={form.formState.isSubmitting}
              size="lg"
              variant="default"
              type="submit"
            >
              Next
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

function getErrorMessage(errorCode?: string) {
  switch (errorCode) {
    case 'OTP_EXPIRED':
      return 'Your verification code has expired. Click resend to try again.';
    case 'INVALID_OTP':
      return "Your verification code doesn't match. Click resend to try again.";
    default:
      return 'Something went wrong. Click resend to try again.';
  }
}
