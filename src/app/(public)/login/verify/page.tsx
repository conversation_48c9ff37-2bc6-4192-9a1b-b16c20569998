import { redirect, RedirectType } from 'next/navigation';
import { OtpForm } from './otp-form';

export default async function LoginVerifyPage({
  searchParams: searchParamsPromise,
}: {
  searchParams: Promise<{
    email?: string;
    redirectTo?: string;
  }>;
}) {
  const { redirectTo, email } = await searchParamsPromise;

  if (!email) {
    return redirect('/login', RedirectType.replace);
  }

  return <OtpForm email={email} redirectTo={redirectTo} />;
}
