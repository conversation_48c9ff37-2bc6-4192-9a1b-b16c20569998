'use client';

import { Button } from '@/components/ui/button';
import { sendMagicLink } from '../actions';
import { useState } from 'react';

export function MagicLink({
  email,
  error,
  redirectTo = '/',
}: {
  email: string;
  error?: string;
  redirectTo?: string;
}) {
  const [loading, setLoading] = useState(false);

  const handleResend = async () => {
    setLoading(true);
    await sendMagicLink(email, redirectTo);
    setLoading(false);
  };

  return (
    <div className="md:max-w-[456px] text-neutral-700 space-y-6">
      <h1 className="title">Check your inbox</h1>
      <p>
        We&apos;ve sent an email to <b>{email}</b> for verification. Click the link in that email to
        complete your setup.
      </p>
      <p>
        Didn&apos;t get it?{' '}
        <Button
          variant="link"
          className="p-0 h-auto text-base font-bold text-secondary-500 underline"
          disabled={loading}
          onClick={handleResend}
        >
          {loading ? 'Resending...' : 'Resend the email'}
        </Button>
      </p>

      {!!error && (
        <div className="text-error text-sm mt-14 space-y-6">
          <p>
            {error}
            {/* You&apos;ve reached the limit for resending emails. Double-check the
              address you entered. */}
          </p>
          <p>
            Still stuck?{' '}
            <a className="underline" href={`mailto:${process.env.NEXT_PUBLIC_POSTMARK_FROM_EMAIL}`}>
              Contact our support team
            </a>{' '}
            and we&apos;ll help you out.
          </p>
        </div>
      )}
    </div>
  );
}
