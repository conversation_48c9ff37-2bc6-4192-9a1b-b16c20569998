import { redirect } from 'next/navigation';
import { MagicLink } from './magic-link';

export default async function LoginEmailPage({
  searchParams: searchParamsPromise,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const searchParams = await searchParamsPromise;
  const email = searchParams.email as string;
  const error = searchParams.error as string;
  const redirectTo = searchParams.redirectTo as string;

  if (!email) {
    return redirect('/login');
  }

  return <MagicLink email={email} error={error} redirectTo={redirectTo} />;
}
