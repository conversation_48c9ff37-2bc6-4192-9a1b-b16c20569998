'use server';

import { headers } from 'next/headers';
import { auth, handleApiError } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { LoginType } from '@/types/auth';

export async function login({ email, callbackURL }: { email: string; callbackURL: string }) {
  const newUser = await isNewUser(email);
  if (newUser) {
    return sendEmailOTP(email);
  }
  return sendMagicLink(email, callbackURL);
}

export async function sendMagicLink(email: string, callbackURL: string) {
  try {
    const { status } = await auth.api.signInMagicLink({
      headers: await headers(),
      body: {
        email,
        callbackURL,
      },
    });
    return { data: { status, type: LoginType.MAGIC_LINK }, error: null };
  } catch (error) {
    return handleApiError(error);
  }
}

export async function sendEmailOTP(email: string) {
  try {
    const { success } = await auth.api.sendVerificationOTP({
      headers: await headers(),
      body: {
        email,
        type: 'sign-in',
      },
    });
    return { data: { success, type: LoginType.OTP }, error: null };
  } catch (error) {
    return handleApiError(error);
  }
}

async function isNewUser(email: string) {
  const user = await prisma.cardholder.findUnique({
    where: { email },
  });
  return !user;
}
