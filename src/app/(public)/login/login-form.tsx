'use client';

import { <PERSON><PERSON>ventHandler } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import z from 'zod';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { LoginType } from '@/types/auth';
import { login, sendEmailOTP } from './actions';

const formSchema = z.object({
  email: z.string().email({ message: 'Enter a valid email address' }),
});

const urlMap = {
  [LoginType.MAGIC_LINK]: '/login/email',
  [LoginType.OTP]: '/login/verify',
};

export function LoginForm({
  email = '',
  redirectTo = '/',
}: {
  email?: string;
  redirectTo?: string;
}) {
  const router = useRouter();
  const useOtp = !!email;

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email,
    },
  });

  const submit = async (data: z.infer<typeof formSchema>) => {
    const { data: res, error } = await (useOtp
      ? sendEmailOTP(data.email)
      : login({
          email: data.email,
          callbackURL: encodeURIComponent(redirectTo),
        }));

    const url = res?.type ? urlMap[res.type] : '/login';

    const qs = new URLSearchParams({
      email: data.email,
    });

    if (error) {
      console.log('Error logging in', error);
      qs.set('error', error.message || 'Failed to login. Please try again.');
    }

    qs.set('redirectTo', redirectTo);
    router.push(`${url}?${qs.toString()}`);
  };

  return <LoginFormScreen disabled={useOtp} form={form} onSubmit={form.handleSubmit(submit)} />;
}

function LoginFormScreen({
  disabled,
  form,
  onSubmit,
}: {
  disabled: boolean;
  form: UseFormReturn<z.infer<typeof formSchema>>;
  onSubmit: FormEventHandler<HTMLFormElement>;
}) {
  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className="md:max-w-[475px] space-y-8">
        <h1 className="title">Welcome to Giftzzy Reloadable Cards, powered by Prezzy.</h1>
        <p className="text-neutral-700">
          Spend anywhere Visa is accepted, top up anytime, and enjoy flexible, easy payments.
        </p>

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email address</FormLabel>
              <FormControl>
                <Input
                  className="aria-invalid:text-destructive disabled:opacity-100 disabled:bg-muted"
                  placeholder="<EMAIL>"
                  disabled={disabled}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end mt-16">
          <Button
            disabled={form.formState.isSubmitting}
            size="lg"
            variant="outline-secondary"
            type="submit"
          >
            Submit
          </Button>
        </div>
      </form>
    </Form>
  );
}
