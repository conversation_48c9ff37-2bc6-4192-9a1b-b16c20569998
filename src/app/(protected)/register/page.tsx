import { CardType } from '@prisma/client';
import { RegisterCardWizard } from '@/components/register-wizard';
import { verifyToken } from './actions';
import { WelcomeLayoutError } from '@/components/welcome-layout/welcome-layout-error';
import { CardRegistrationErrorCode } from '@/types/errors';
import { getCardholderById } from '@/db/cardholder';
import { getCurrentSession } from '@/lib/auth';
import { redirect } from 'next/navigation';

export default async function RegisterPage({
  searchParams: searchParamsPromise,
}: {
  searchParams: Promise<{ token?: string }>;
}) {
  const { token } = await searchParamsPromise;
  const session = await getCurrentSession();

  if (!session) {
    redirect('/login');
  }

  const cardholder = await getCardholderById(session.user.id);

  if (!cardholder) {
    redirect('/login');
  }

  if (!token) {
    return <RegisterCardWizard cardType={CardType.PHYSICAL} phone={cardholder.phone} />;
  }

  const { data, error } = await verifyToken(token);

  if (error || !data) {
    return <WelcomeLayoutError error={error as CardRegistrationErrorCode} />;
  }

  return <RegisterCardWizard cardType={CardType.VIRTUAL} crn={data.crn} phone={cardholder.phone} />;
}
