'use server';

import { redirect } from 'next/navigation';
import { getTokenById, invalidateToken } from '@/db/token';
import { getCurrentSession } from '@/lib/auth';
import { validatePhysicalLockCode } from '@/lib/ren';
import { CardRegistrationErrorCode } from '@/types/errors';

export async function verifyToken(token: string) {
  const session = await getCurrentSession();

  if (!session) {
    console.error('Not authenticated');
    redirect('/login');
  }

  try {
    const data = await getTokenById(token);

    if (!data) {
      throw new Error(CardRegistrationErrorCode.INVALID_TOKEN);
    }

    if (data.expires < new Date()) {
      throw new Error(CardRegistrationErrorCode.EXPIRED_TOKEN);
    }

    if (data.email !== session.user.email) {
      throw new Error(CardRegistrationErrorCode.INVALID_EMAIL);
    }

    if (data.usedCount > 0) {
      throw new Error(CardRegistrationErrorCode.TOKEN_ALREADY_USED);
    }

    await invalidateToken(token);
    return { data, error: null };
  } catch (error) {
    console.error('verifyToken error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to verify token';
    return { error: errorMessage, data: null };
  }
}

export async function submitLockCode(crn: string, lockCode: string) {
  return validatePhysicalLockCode(crn, lockCode);
}
