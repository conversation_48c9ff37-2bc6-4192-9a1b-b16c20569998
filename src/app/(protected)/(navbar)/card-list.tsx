import { Button } from '@/components/ui/button';
import { cn } from '@/utils/cn';
import { Card, CardType } from '@prisma/client';
import { Plus } from 'lucide-react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { capitalize } from '@/utils/string';

export function CardList({ cards }: { cards: Card[] }) {
  return (
    <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
      {cards.map((card) => (
        <CardItem
          key={card.id}
          link={`/card/${card.id}`}
          description={
            <div>
              <Badge variant={card.type === CardType.PHYSICAL ? 'success' : 'pink'}>
                {capitalize(card.type)}
              </Badge>
              <p className="heading-m mt-2">•••• •••• •••• {card.crn.slice(12, 16)}</p>
            </div>
          }
        />
      ))}
      <RegisterCard />
    </div>
  );
}

function RegisterCard() {
  return (
    <CardItem
      className="bg-neutral-100"
      link="/register"
      image={<Plus width={80} height={80} color="#000000" strokeWidth={3} />}
      description={
        <Button className="w-full" size="lg" variant="outline">
          Register a card
        </Button>
      }
    />
  );
}

function CardItem({
  className,
  link,
  image,
  description,
}: {
  className?: string;
  link: string;
  image?: React.ReactNode;
  description: React.ReactNode;
}) {
  return (
    <Link
      className={cn(
        'bg-white rounded-2xl border border-neutral-200 hover:border-neutral-700',
        className,
      )}
      href={link}
    >
      <div className="w-full min-h-[200px] rounded-2xl flex justify-center items-center bg-[#D5D5D5]">
        {image}
      </div>
      <div className="px-4 py-6">{description}</div>
    </Link>
  );
}
