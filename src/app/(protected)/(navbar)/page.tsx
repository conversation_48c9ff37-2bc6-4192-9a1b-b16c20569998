import { RegisterCardSuccess } from '@/components/modals';
import { CardList } from './card-list';
import { getCards } from './actions';

export default async function ProtectedPage({
  searchParams: searchParamsPromise,
}: {
  searchParams: Promise<{ modal?: string }>;
}) {
  const { modal } = await searchParamsPromise;
  const cards = await getCards();

  return (
    <>
      <RegisterCardSuccess open={modal === 'register-success'} />
      <div className="p-10 bg-white rounded-3xl">
        <h1 className="heading-xl mb-8">Cards</h1>
        <CardList cards={cards} />
      </div>
    </>
  );
}
