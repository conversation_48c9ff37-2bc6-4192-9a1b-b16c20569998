import type { Metadata } from 'next';
import localFont from 'next/font/local';
import './globals.css';

export const metadata: Metadata = {
  title: 'Epay Cardholder Portal',
  description: 'Epay Cardholder Portal',
};

const geograph = localFont({
  src: [
    {
      path: '../../public/fonts/geograph-thin.woff2',
      weight: '100',
      style: 'thin',
    },
    {
      path: '../../public/fonts/geograph-light.woff2',
      weight: '300',
      style: 'light',
    },
    {
      path: '../../public/fonts/geograph-regular.woff2',
      weight: '400',
      style: 'regular',
    },
    {
      path: '../../public/fonts/geograph-medium.woff2',
      weight: '500',
      style: 'medium',
    },
    {
      path: '../../public/fonts/geograph-bold.woff2',
      weight: '700',
      style: 'bold',
    },
    {
      path: '../../public/fonts/geograph-black.woff2',
      weight: '900',
      style: 'black',
    },
  ],
  variable: '--geograph',
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`antialiased ${geograph.variable} min-h-screen`}>{children}</body>
    </html>
  );
}
