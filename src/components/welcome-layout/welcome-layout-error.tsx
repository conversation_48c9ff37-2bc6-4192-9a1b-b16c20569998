import Link from 'next/link';
import config from '@/config/client';
import { AppErrorCode, AuthErrorCode, CardRegistrationErrorCode } from '@/types/errors';
import { cn } from '@/utils/cn';
import { Button } from '../ui/button';

export function WelcomeLayoutError({
  error,
  className,
  backHref,
  backText = 'Back to login',
}: {
  error: AppErrorCode;
  className?: string;
  backHref?: string;
  backText?: string;
}) {
  const title = getTitle(error);
  const message = getMessage(error);
  return (
    <div className={cn('md:max-w-[456px] text-neutral-700 space-y-10', className)}>
      <h1 className="title">{title}</h1>
      <p>
        {message} If you require any assistance,{' '}
        <a className="underline" href={`mailto:${config.postmarkFromEmail}`}>
          contact our support team
        </a>{' '}
        and we&apos;ll help you out.
      </p>
      {!!backHref && (
        <Button asChild className="" size="lg" variant="outline-secondary" type="submit">
          <Link href={backHref}>{backText}</Link>
        </Button>
      )}
    </div>
  );
}

function getTitle(error: AppErrorCode) {
  switch (error) {
    case AuthErrorCode.EXPIRED_TOKEN:
    case CardRegistrationErrorCode.EXPIRED_TOKEN:
      return 'Link expired';
    case AuthErrorCode.INVALID_TOKEN:
    case CardRegistrationErrorCode.INVALID_TOKEN:
    case CardRegistrationErrorCode.TOKEN_ALREADY_USED:
      return 'Invalid link';
    case CardRegistrationErrorCode.INVALID_EMAIL:
      return 'Invalid email';
    default:
      return 'Something went wrong';
  }
}

function getMessage(error: AppErrorCode) {
  switch (error) {
    case AuthErrorCode.EXPIRED_TOKEN:
    case CardRegistrationErrorCode.EXPIRED_TOKEN:
      return 'The link you clicked has expired.';
    case AuthErrorCode.INVALID_TOKEN:
    case CardRegistrationErrorCode.INVALID_TOKEN:
      return 'The link you clicked is invalid.';
    case CardRegistrationErrorCode.INVALID_EMAIL:
      return 'The link you clicked is not valid for this email.';
    case CardRegistrationErrorCode.TOKEN_ALREADY_USED:
      return 'The link you clicked has already been used.';
    default:
      return 'Please try again.';
  }
}
