import Image from 'next/image';
import Link from 'next/link';

export function WelcomeLayout({ children }: { children: React.ReactNode }) {
  const year = new Date().getFullYear();

  return (
    <div className="flex flex-col min-h-screen min-w-screen p-7 bg-[#DFE1E5]">
      <Link className="cursor-pointer mb-6" href="/">
        <Image width={80} height={32} src="/images/prezzy.png" alt="Prezzycard" />
      </Link>
      <div className="rounded-2xl flex flex-1 max-md:flex-col w-full h-full overflow-hidden border border-neutral-200">
        <div className="max-md:order-1 md:w-1/2 gradient-primary text-white text-center flex flex-col justify-end p-10">
          <div className="flex-2"></div>
          <div className="flex flex-1 flex-col justify-between">
            <h1 className="title max-w-[375px] mx-auto text-white">
              New Zealand’s #1 reloadable card
            </h1>
            <p className="text-xs mt-6">{year} All Rights Reserved Prezzycard</p>
          </div>
        </div>
        <div className="max-md:flex-1 md:w-1/2 bg-white flex items-center justify-center p-10">
          {children}
        </div>
      </div>
    </div>
  );
}
