'use client';

import { cn } from '@/utils/cn';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { Button } from '../ui/button';
import { authClient } from '@/lib/auth-client';

const links = [
  {
    name: 'Dashboard',
    href: '/',
  },
  {
    name: 'Help',
    href: '/help',
  },
  {
    name: 'Account',
    href: '/settings',
  },
];

export function Navbar() {
  const router = useRouter();
  const pathname = usePathname();

  const handleSignout = async () => {
    await authClient.signOut({
      fetchOptions: {
        onSuccess: () => {
          router.push('/login');
        },
      },
    });
  };

  return (
    <div className="min-h-16 px-8 py-3 bg-white flex justify-between items-center">
      <Link className="cursor-pointer" href="/">
        <Image width={80} height={32} src="/images/prezzy.png" alt="Prezzycard" />
      </Link>

      <div className="flex flex-1 justify-end gap-12 items-center">
        {links.map((l) => (
          <Link
            key={l.name}
            className={cn(
              'hover:font-medium',
              pathname === l.href && 'font-medium hover:font-medium text-secondary-500',
            )}
            href={l.href}
          >
            {l.name}
          </Link>
        ))}
        <Button
          className="p-0 text-base font-normal text-neutral-800 hover:no-underline hover:font-medium"
          onClick={handleSignout}
          variant="link"
        >
          Sign out
        </Button>
      </div>
    </div>
  );
}
