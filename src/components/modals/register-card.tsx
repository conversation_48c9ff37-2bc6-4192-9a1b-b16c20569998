'use client';

import { useRouter } from 'next/navigation';
import { Button } from '../ui/button';
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogTitle } from '../ui/dialog';

export function RegisterCardSuccess({ open }: { open: boolean }) {
  const router = useRouter();

  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      router.replace('/');
    }
  };

  return (
    <Dialog defaultOpen={open} onOpenChange={handleOpenChange}>
      <DialogContent className="fixed left-1/2 top-1/2 max-h-[85vh] w-[90vw] max-w-[500px] -translate-x-1/2 -translate-y-1/2 focus:outline-none data-[state=open]:animate-contentShow">
        <DialogTitle className="m-0">Your card is ready to go! 🎉</DialogTitle>
        <DialogDescription className="body-l">
          You can now use your Giftzzy card anywhere Visa is accepted. Funds running low? Top up
          anytime and keep the fun rolling.
        </DialogDescription>
        <div className="mt-[25px] flex justify-end">
          <DialogClose asChild>
            <Button className="">Go to cards dashboard</Button>
          </DialogClose>
        </div>
      </DialogContent>
    </Dialog>
  );
}
