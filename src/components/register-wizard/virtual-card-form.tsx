'use client';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Checkbox } from '@/components/ui/checkbox';
import { VirtualCardInfo, virtualCardSchema } from '@/hooks/register-wizard';
import { Buttons } from './buttons';

export function VirtualCardForm({
  defaultValues,
  onBack,
  onSubmit,
}: {
  defaultValues: VirtualCardInfo;
  onBack: () => void;
  onSubmit: (data: VirtualCardInfo) => void;
}) {
  const form = useForm({
    resolver: zodResolver(virtualCardSchema),
    defaultValues: {
      crn: defaultValues?.crn || '',
      terms: defaultValues?.terms || false,
    },
  });

  return (
    <div className="flex flex-col gap-4 max-w-[475px] mx-auto">
      <h1 className="title">Your card reference number (CRN)</h1>
      <p className="text-sm text-neutral-700">
        This CRN is in your registration email. Can’t find it? Contact us at{' '}
        <a className="underline" href={`mailto:${process.env.NEXT_PUBLIC_POSTMARK_FROM_EMAIL}`}>
          {process.env.NEXT_PUBLIC_POSTMARK_FROM_EMAIL}
        </a>
        .
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 mt-12">
          <FormField
            control={form.control}
            name="crn"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Card Reference Number (CRN)</FormLabel>
                <FormControl>
                  <Input
                    className="disabled:opacity-100 disabled:bg-muted"
                    disabled
                    maxLength={19}
                    inputMode="numeric"
                    placeholder="XXXX - XXXX - XXXX - XXXX"
                    {...field}
                    value={field.value
                      .replace(/[^\d]/g, '')
                      .replace(/(.{4})/g, '$1-')
                      .replace(/-$/, '')}
                    onChange={(e) => {
                      const rawValue = e.target.value.replace(/[^\d]/g, '');
                      field.onChange(rawValue);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="terms"
            render={({ field }) => (
              <FormItem className="mt-15">
                <div className="flex items-center justify-start gap-2 ">
                  <FormControl>
                    <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel className="text-base text-neutral-700 font-normal">
                      I agree to the{' '}
                      <a
                        className="underline"
                        href="/terms"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        terms & conditions
                      </a>
                    </FormLabel>
                  </div>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <Buttons disabled={form.formState.isSubmitting} hideBack={true} onBack={onBack} />
        </form>
      </Form>
    </div>
  );
}
