'use client';

import {
  PhysicalCardInfo,
  PinInfo,
  RegisterWizardStep,
  useRegisterWizard,
  VirtualCardInfo,
} from '@/hooks/register-wizard';
import { CardType } from '@prisma/client';
import { PhysicalCardForm } from './physical-card-form';
import { VirtualCardForm } from './virtual-card-form';
import { PinForm } from './pin-form';
import { PhoneOtpForm } from './phone-otp-form';
import ContactForm from './contact-form';
import { LockCodeForm } from './lock-code-form';

export function RegisterCardWizard({
  cardType,
  crn,
  phone,
}: {
  cardType: CardType;
  crn?: string;
  phone?: string | null;
}) {
  const {
    cardInfo,
    pinInfo,
    step,
    contactInfo,
    loading,
    onBack,
    onCardFormSubmit,
    onLockFormSubmit,
    onPinFormSubmit,
    onContactFormSubmit,
    onOtpFormSubmit,
    resendOTP,
  } = useRegisterWizard({ cardType, crn, phone });

  switch (step) {
    case RegisterWizardStep.CARD_FORM:
      return cardType === CardType.PHYSICAL ? (
        <PhysicalCardForm
          defaultValues={cardInfo as PhysicalCardInfo}
          onBack={onBack}
          onSubmit={onCardFormSubmit}
        />
      ) : (
        <VirtualCardForm
          defaultValues={cardInfo as VirtualCardInfo}
          onBack={onBack}
          onSubmit={onCardFormSubmit}
        />
      );
    case RegisterWizardStep.LOCK_CODE_FORM:
      return <LockCodeForm crn={cardInfo.crn} onBack={onBack} onSubmit={onLockFormSubmit} />;
    case RegisterWizardStep.PIN_FORM:
      return (
        <PinForm defaultValues={pinInfo as PinInfo} onBack={onBack} onSubmit={onPinFormSubmit} />
      );
    case RegisterWizardStep.CONTACT_INFO_FORM:
      return <ContactForm onBack={onBack} onSubmit={onContactFormSubmit} />;
    case RegisterWizardStep.OTP_FORM:
      return (
        <PhoneOtpForm
          phone={contactInfo?.phone || ''}
          onBack={onBack}
          onSubmit={onOtpFormSubmit}
          onResend={resendOTP}
          loading={loading}
        />
      );
    default:
      return null;
  }
}
