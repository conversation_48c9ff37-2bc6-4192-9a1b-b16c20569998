'use client';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Checkbox } from '@/components/ui/checkbox';
import config from '@/config/client';
import { PhysicalCardInfo, physicalCardSchema } from '@/hooks/register-wizard';
import { Buttons } from './buttons';
import { validatePhysicalCardSubmit } from '@/hooks/register-wizard/actions';

export function PhysicalCardForm({
  defaultValues,
  onBack,
  onSubmit,
}: {
  defaultValues: PhysicalCardInfo;
  onBack: () => void;
  onSubmit: (data: PhysicalCardInfo) => void;
}) {
  const form = useForm({
    resolver: zodResolver(physicalCardSchema),
    defaultValues: {
      crn: defaultValues?.crn || '',
      cvv: defaultValues?.cvv || '',
      expiry: defaultValues?.expiry || '',
      terms: defaultValues?.terms || false,
    },
  });

  const submit = async (data: PhysicalCardInfo) => {
    const isValid = await validatePhysicalCardSubmit(data);
    if (isValid) {
      onSubmit(data);
    } else {
      form.setError('root.validation', {
        message: 'Invalid card information. Please check your card details and try again.',
      });
    }
  };

  return (
    <div className="flex flex-col gap-4 max-w-[475px] mx-auto">
      <h1 className="title">Enter your card details</h1>
      <p className="text-sm text-neutral-700">
        Your CRN is in your registration email. Can’t find it? Contact us at{' '}
        <a className="underline" href={`mailto:${config.postmarkFromEmail}`}>
          {config.postmarkFromEmail}
        </a>
        .
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(submit)} className="space-y-6 mt-12">
          <FormField
            control={form.control}
            name="crn"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Card Reference Number (CRN)</FormLabel>
                <FormControl>
                  <Input
                    maxLength={19}
                    inputMode="numeric"
                    placeholder="XXXX - XXXX - XXXX - XXXX"
                    {...field}
                    value={field.value
                      .replace(/[^\d]/g, '')
                      .replace(/(.{4})/g, '$1-')
                      .replace(/-$/, '')}
                    onChange={(e) => {
                      const rawValue = e.target.value.replace(/[^\d]/g, '');
                      field.onChange(rawValue);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex items-start gap-15">
            <FormField
              control={form.control}
              name="cvv"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>CVV</FormLabel>
                  <FormControl>
                    <Input
                      maxLength={4}
                      inputMode="numeric"
                      placeholder="XXX"
                      {...field}
                      value={field.value?.replace(/[^\d]/g, '') || ''}
                      onChange={(e) => {
                        const rawValue = e.target.value.replace(/[^\d]/g, '');
                        field.onChange(rawValue);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="expiry"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Expiry</FormLabel>
                  <FormControl>
                    <Input
                      maxLength={5}
                      inputMode="numeric"
                      placeholder="MM/YY"
                      {...field}
                      value={field.value
                        ?.replace(/[^\d]/g, '')
                        .replace(/^(\d{2})(\d{0,2})/, '$1/$2')
                        .replace(/^\//, '')}
                      onChange={(e) => {
                        const rawValue = e.target.value.replace(/[^\d]/g, '');
                        if (rawValue.length <= 4) {
                          field.onChange(rawValue);
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {!!form.formState.errors.root?.validation && (
            <p className="text-destructive mt-4 space-y-6">
              {form.formState.errors.root.validation.message}
            </p>
          )}

          <FormField
            control={form.control}
            name="terms"
            render={({ field }) => (
              <FormItem className="mt-15">
                <div className="flex items-center justify-start gap-2 ">
                  <FormControl>
                    <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel className="text-base text-neutral-700 font-normal">
                      I agree to the{' '}
                      <a
                        className="underline"
                        href="/terms"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        terms & conditions
                      </a>
                    </FormLabel>
                  </div>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <Buttons disabled={form.formState.isSubmitting} hideBack={true} onBack={onBack} />
        </form>
      </Form>
    </div>
  );
}
