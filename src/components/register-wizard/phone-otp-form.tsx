'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { InputOTP, InputOTPSlot } from '@/components/ui/input-otp';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { PhoneOtpInfo, phoneOtpSchema, verifyOTP } from '@/hooks/register-wizard';
import { Buttons } from './buttons';

export function PhoneOtpForm({
  loading,
  phone,
  onBack,
  onResend,
  onSubmit,
}: {
  loading: boolean;
  phone: string;
  onBack: () => void;
  onResend: () => void;
  onSubmit: (data: PhoneOtpInfo) => void;
}) {
  const form = useForm({
    resolver: zodResolver(phoneOtpSchema),
    defaultValues: {
      otp: '',
    },
  });

  const submit = async (data: PhoneOtpInfo) => {
    const { error } = await verifyOTP(data.otp);
    if (error) {
      form.setError('otp', { message: 'Invalid code' });
    } else {
      onSubmit(data);
    }
  };

  return (
    <div className="flex flex-col gap-4 max-w-[450px] mx-auto">
      <h1 className="title">Verify your phone number</h1>
      <p className="text-neutral-700">
        Enter the 6-digit code we sent to the phone number ending in{' '}
        <b>*** *** {phone?.slice(-4)}</b>
      </p>
      <Form {...form}>
        <form className="space-y-10 mt-10" onSubmit={form.handleSubmit(submit)}>
          <FormField
            control={form.control}
            name="otp"
            render={({ field }) => (
              <FormItem className="w-fit">
                <FormLabel className="flex items-center justify-between">
                  <span className="text-base">Enter the code</span>
                  <Button
                    type="button"
                    variant="link"
                    size="sm"
                    className="p-0 h-auto text-base font-bold text-primary-500 underline"
                    disabled={loading}
                    onClick={onResend}
                  >
                    Resend
                  </Button>
                </FormLabel>
                <FormControl>
                  <InputOTP maxLength={6} {...field} pattern="\d*">
                    <InputOTPSlot index={0} />
                    <InputOTPSlot index={1} />
                    <InputOTPSlot index={2} />
                    <InputOTPSlot index={3} />
                    <InputOTPSlot index={4} />
                    <InputOTPSlot index={5} />
                  </InputOTP>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Buttons disabled={form.formState.isSubmitting} onBack={onBack} />
        </form>
      </Form>
    </div>
  );
}
