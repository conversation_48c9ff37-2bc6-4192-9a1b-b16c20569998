import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { PhoneInput } from '@/components/ui/phone-input';
import { Input } from '@/components/ui/input';
import { contactFormSchema, ContactInfo } from '@/hooks/register-wizard';
import { Buttons } from './buttons';

export default function ContactForm({
  onBack,
  onSubmit,
}: {
  onBack: () => void;
  onSubmit: (data: ContactInfo) => void;
}) {
  const form = useForm({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      phone: '',
    },
  });

  return (
    <div className="flex flex-col gap-4 max-w-[475px] mx-auto">
      <h1 className="title">Enter your contact details</h1>
      <p className="text-sm text-neutral-700">
        Enter your mobile number to receive an SMS verification code.{' '}
        <a
          href={`mailto:${process.env.NEXT_PUBLIC_POSTMARK_FROM_EMAIL}`}
          className="font-bold underline"
        >
          Need help?
        </a>
      </p>
      <Form {...form}>
        <form className="space-y-6 mt-6" onSubmit={form.handleSubmit(onSubmit)}>
          <div className="flex items-start gap-15">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First name</FormLabel>
                  <FormControl>
                    <Input placeholder="John" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last name</FormLabel>
                  <FormControl>
                    <Input placeholder="Smith" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone number</FormLabel>
                <FormControl>
                  <PhoneInput defaultCountry="NZ" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Buttons submitText="Verify" onBack={onBack} />
        </form>
      </Form>
    </div>
  );
}
