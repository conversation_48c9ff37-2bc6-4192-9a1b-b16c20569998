import { CardType } from '@prisma/client';
import { But<PERSON> } from '@/components/ui/button';

export function CardTypeSelect({ onSelect }: { onSelect: (type: CardType) => void }) {
  return (
    <div className="flex flex-col gap-4 max-w-[475px] mx-auto">
      <h1 className="title">Are you registering a physical or virtual card?</h1>
      <p className="text-neutral-700">Choose the option that matches the card you received.</p>
      <div className="flex gap-4 mt-10">
        <Button
          className="flex flex-col justify-end flex-1 h-auto text-base font-normal min-h-[132px] p-6"
          variant="outline"
          onClick={() => onSelect(CardType.VIRTUAL)}
        >
          <div className="bg-[#D9D9D9] w-20 h-10 mb-3 mx-auto"></div>
          Virtual Card
        </Button>
        <Button
          className="flex flex-col justify-end flex-1 h-auto text-base font-normal min-h-[132px] p-6"
          variant="outline"
          onClick={() => onSelect(CardType.PHYSICAL)}
        >
          <div className="bg-[#D9D9D9] w-10 h-15 mb-3 mx-auto"></div>
          Physical Card
        </Button>
      </div>
    </div>
  );
}
