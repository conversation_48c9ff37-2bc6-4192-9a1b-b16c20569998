'use client';

import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/utils/cn';
import { ArrowLeftIcon } from 'lucide-react';

export function Buttons({
  className,
  disabled,
  hideBack,
  submitText = 'Next',
  onBack,
}: {
  className?: string;
  disabled?: boolean;
  hideBack?: boolean;
  submitText?: string;
  onBack: () => void;
}) {
  return (
    <div className={cn('flex justify-between mt-16', className)}>
      {!hideBack && (
        <Button
          type="button"
          variant="link"
          className="p-0! h-auto text-base font-bold text-secondary-500"
          onClick={onBack}
        >
          <ArrowLeftIcon className="size-5" /> Back
        </Button>
      )}
      <Button disabled={disabled} size="lg" variant="default" type="submit">
        {submitText}
      </Button>
    </div>
  );
}
