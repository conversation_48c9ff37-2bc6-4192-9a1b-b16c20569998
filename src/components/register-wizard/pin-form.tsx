'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { InputOTP, InputOTPSlot } from '@/components/ui/input-otp';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { PinInfo, pinSchema } from '@/hooks/register-wizard';
import { Buttons } from './buttons';

export function PinForm({
  defaultValues,
  onBack,
  onSubmit,
}: {
  defaultValues: PinInfo;
  onBack: () => void;
  onSubmit: (data: PinInfo) => void;
}) {
  const form = useForm({
    resolver: zodResolver(pinSchema),
    defaultValues: defaultValues || {
      pin: '',
      confirmPin: '',
    },
  });

  return (
    <div className="flex flex-col gap-4 max-w-[475px] mx-auto">
      <h1 className="title">Set your PIN code</h1>
      <p className="text-neutral-700">
        Choose a 4-digit PIN for your card. You may need this when making payments at physical
        stores.
      </p>
      <Form {...form}>
        <form className="space-y-10 mt-10" onSubmit={form.handleSubmit(onSubmit)}>
          <FormField
            control={form.control}
            name="pin"
            render={({ field }) => (
              <FormItem className="w-fit">
                <FormLabel>Enter PIN code</FormLabel>
                <FormControl>
                  <InputOTP maxLength={6} {...field} pattern="\d*">
                    <InputOTPSlot index={0} />
                    <InputOTPSlot index={1} />
                    <InputOTPSlot index={2} />
                    <InputOTPSlot index={3} />
                  </InputOTP>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmPin"
            render={({ field }) => (
              <FormItem className="w-fit">
                <FormLabel>Confirm PIN code</FormLabel>
                <FormControl>
                  <InputOTP maxLength={6} {...field} pattern="\d*">
                    <InputOTPSlot index={0} />
                    <InputOTPSlot index={1} />
                    <InputOTPSlot index={2} />
                    <InputOTPSlot index={3} />
                  </InputOTP>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Buttons disabled={form.formState.isSubmitting} onBack={onBack} />
        </form>
      </Form>
    </div>
  );
}
