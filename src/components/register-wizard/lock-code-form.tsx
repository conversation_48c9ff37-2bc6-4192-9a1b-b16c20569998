'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { InputOTP, InputOTPSlot } from '@/components/ui/input-otp';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { LockCodeInfo, lockCodeSchema, validateLockCodeSubmit } from '@/hooks/register-wizard';
import { Buttons } from './buttons';

export function LockCodeForm({
  crn,
  onBack,
  onSubmit,
}: {
  crn: string;
  onBack: () => void;
  onSubmit: (data: LockCodeInfo) => void;
}) {
  const form = useForm({
    resolver: zodResolver(lockCodeSchema),
    defaultValues: {
      lockCode: '',
    },
  });

  const submit = async (data: LockCodeInfo) => {
    const isValid = await validateLockCodeSubmit(crn, data.lockCode);
    if (isValid) {
      onSubmit(data);
    } else {
      form.setError('lockCode', { message: 'Invalid access code' });
    }
  };

  return (
    <div className="flex flex-col gap-4 max-w-[450px] mx-auto">
      <h1 className="title">Enter access code</h1>
      <p className="text-neutral-700">
        Enter the 4-digit access code we sent to your email for this card
      </p>
      <Form {...form}>
        <form className="space-y-10 mt-10" onSubmit={form.handleSubmit(submit)}>
          <FormField
            control={form.control}
            name="lockCode"
            render={({ field }) => (
              <FormItem className="w-fit">
                <FormLabel className="flex items-center justify-between">
                  Enter access code
                </FormLabel>
                <FormControl>
                  <InputOTP maxLength={6} {...field} pattern="\d*">
                    <InputOTPSlot index={0} />
                    <InputOTPSlot index={1} />
                    <InputOTPSlot index={2} />
                    <InputOTPSlot index={3} />
                  </InputOTP>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Buttons disabled={form.formState.isSubmitting} onBack={onBack} />
        </form>
      </Form>
    </div>
  );
}
