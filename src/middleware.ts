import { headers } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import serverConfig from '@/config/server';

export default async function middleware(request: NextRequest) {
  // Redirect to login page if there is an error in register
  const error = request.nextUrl.searchParams.get('error');
  if (error && request.nextUrl.pathname.startsWith('/register')) {
    console.error('Error in register', error);
    return NextResponse.redirect(new URL(`/login?error=${error}`, request.url));
  }

  const session = await getAuthSession();
  if (!session) {
    return redirectToLogin(request);
  }

  return NextResponse.next();
}

export const config = {
  /*
   * Match all request paths except for the ones starting with:
   * - api (API routes)
   * - _next/static (static files)
   * - _next/image (image optimization files)
   * - favicon.ico, sitemap.xml, robots.txt (metadata files)
   */
  matcher: '/((?!api|_next/static|_next/image|favicon.ico|login|activate).*)',
};

function getRedirectWithQuery(nextUrl: NextRequest['nextUrl']) {
  let redirectTo = nextUrl.pathname;
  const searchParams = nextUrl.searchParams.toString();
  if (searchParams) {
    redirectTo += `?${searchParams}`;
  }
  return redirectTo;
}

function redirectToLogin(request: NextRequest) {
  const url = new URL('/login', request.url);

  // If not signed in, redirect register with token to login page with token
  const registrationToken = request.nextUrl.searchParams.get('token');
  if (registrationToken) {
    url.searchParams.set('token', registrationToken);
  }

  // Redirect to login page if not authenticated
  // Create redirectTo with pathname and query params
  if (request.nextUrl.pathname !== '/' || request.nextUrl.pathname.startsWith('/login')) {
    url.searchParams.set('redirectTo', getRedirectWithQuery(request.nextUrl));
  }

  return NextResponse.redirect(url);
}

// Currently the only way to get auth session from DB in the middleware
async function getAuthSession() {
  return fetch(`${serverConfig.auth.baseUrl}/api/auth/get-session`, {
    headers: await headers(),
  })
    .then((res) => res.json())
    .catch(() => null);
}
