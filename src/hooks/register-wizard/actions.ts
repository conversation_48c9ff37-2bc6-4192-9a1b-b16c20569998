'use server';

import { redirect } from 'next/navigation';
import { sendVerification, checkVerification } from '@/lib/twilio';
import { createCard } from '@/db/card';
import { updateCardholderById } from '@/db/cardholder';
import { getOtpForCardholder, incrementOtpAttemptCount, upsertOtpForCardholder } from '@/db/otp';
import { getCurrentSession } from '@/lib/auth';
import {
  registerPhysicalCard,
  registerVirtualCard,
  validatePhysicalCard,
  validatePhysicalLockCode,
} from '@/lib/ren';
import { CardRegistrationErrorCode } from '@/types/errors';
import { PhysicalCardInfo } from './types';
import { CardType } from '@prisma/client';

export async function sendOTP(phoneNumber: string, channel: 'sms' | 'whatsapp') {
  const session = await getCurrentSession();

  if (!session) {
    console.error('Not authenticated');
    redirect('/login');
  }

  const cardholderId = session?.user.id;

  try {
    //1. Save to db
    await upsertOtpForCardholder(cardholderId, phoneNumber);

    //2. Send OTP
    return sendVerification(phoneNumber, channel);
  } catch (ex) {
    console.error('sendOTP error:', ex);
    return { error: 'Failed to send OTP' };
  }
}

export async function verifyOTP(code: string) {
  const session = await getCurrentSession();

  if (!session) {
    console.error('Not authenticated');
    redirect('/login');
  }

  const cardholderId = session.user.id;

  try {
    //1. Get phone number
    const otp = await getOtpForCardholder(cardholderId);

    if (!otp) {
      throw new Error(`No OTP found for user ${cardholderId}`);
    }

    //2. Verify OTP
    const { data, error } = await checkVerification(otp.phoneNumber, code);

    if (error) {
      //2a. Increment attempt on verification error
      await incrementOtpAttemptCount(cardholderId);
      throw new Error(error);
    }

    //2b. Update phone details for cardholder
    const cardholder = await updateCardholderById({
      id: cardholderId,
      phone: otp.phoneNumber,
      phoneVerified: true,
    });

    return { data: { otp: data, cardholder }, error: null };
  } catch (ex) {
    console.error('verifyOTP error:', ex);
    return { error: 'Failed to verify OTP' };
  }
}

export async function validatePhysicalCardSubmit(data: PhysicalCardInfo) {
  return validatePhysicalCard(data);
}

export async function validateLockCodeSubmit(crn: string, lockCode: string) {
  return validatePhysicalLockCode(crn, lockCode);
}

export async function submitPhysicalCardRegistration(data: {
  crn: string;
  cvv: string;
  expiry: string;
  pin: string;
  confirmPin: string;
  lockCode: string;
}) {
  try {
    if (data.pin !== data.confirmPin) {
      throw new Error(CardRegistrationErrorCode.FAILED_TO_REGISTER);
    }

    const session = await getCurrentSession();

    const { error: registerError } = await registerPhysicalCard(data);

    if (registerError) {
      throw new Error(CardRegistrationErrorCode.FAILED_TO_REGISTER);
    }

    const card = await createCard({
      crn: data.crn,
      cardholderId: session?.user.id as string,
      type: CardType.PHYSICAL,
    });

    return { data: card, error: null };
  } catch (error) {
    console.error('submitPhysicalCardRegistration error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to register card';
    return { error: errorMessage, data: null };
  }
}

export async function submitVirtualCardRegistration({ crn }: { crn: string }) {
  try {
    const session = await getCurrentSession();
    const { error } = await registerVirtualCard({ crn });

    if (error) {
      throw new Error(CardRegistrationErrorCode.FAILED_TO_REGISTER);
    }

    const card = await createCard({
      crn,
      cardholderId: session?.user.id as string,
      type: CardType.VIRTUAL,
    });
    return { data: card, error: null };
  } catch (error) {
    console.error('submitVirtualCardRegistration error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to register card';
    return { error: errorMessage, data: null };
  }
}
