import { z } from 'zod';
import {
  contactFormSchema,
  phoneOtpSchema,
  pinSchema,
  physicalCardSchema,
  virtualCardSchema,
  lockCodeSchema,
} from './schemas';

export type ContactInfo = z.infer<typeof contactFormSchema>;
export type PinInfo = z.infer<typeof pinSchema>;
export type PhoneOtpInfo = z.infer<typeof phoneOtpSchema>;
export type PhysicalCardInfo = z.infer<typeof physicalCardSchema>;
export type VirtualCardInfo = z.infer<typeof virtualCardSchema>;
export type LockCodeInfo = z.infer<typeof lockCodeSchema>;

export enum RegisterWizardStep {
  CARD_TYPE_SELECT = 'card-type-select',
  CARD_FORM = 'card-form',
  PIN_FORM = 'pin-form',
  CONTACT_INFO_FORM = 'contact-info-form',
  LOCK_CODE_FORM = 'lock-code-form',
  OTP_FORM = 'otp-form',
}
