import z from 'zod';

export const pinSchema = z
  .object({
    pin: z.string().length(4, { message: 'Invalid pin' }),
    confirmPin: z.string().length(4, { message: 'Invalid pin' }),
  })
  .refine((data) => data.pin === data.confirmPin, {
    message: 'Pins do not match',
    path: ['confirmPin'],
  });

export const contactFormSchema = z.object({
  firstName: z.string().min(1, { message: 'First name is required' }),
  lastName: z.string().min(1, { message: 'Last name is required' }),
  phone: z.string().min(10, { message: 'Invalid phone number' }),
});

export const phoneOtpSchema = z.object({
  otp: z.string().length(6, { message: 'Invalid code' }),
});

export const physicalCardSchema = z.object({
  crn: z.string().min(16, { message: 'Invalid card number' }),
  cvv: z.string().min(3, { message: 'Invalid CVV' }).max(4, { message: 'Invalid CVV' }),
  expiry: z
    .string()
    .length(4, { message: 'Invalid expiry date' })
    .refine(
      (value) => {
        const month = value.slice(0, 2);
        const year = value.slice(2, 4);
        return month >= '01' && month <= '12' && year >= '23' && year <= '99';
      },
      { message: 'Invalid expiry date' },
    ),
  terms: z.boolean().refine((value) => value === true, {
    message: 'You must agree to the terms and conditions',
  }),
});

export const virtualCardSchema = z.object({
  crn: z.string().min(16, { message: 'Invalid card number' }),
  terms: z.boolean().refine((value) => value === true, {
    message: 'You must agree to the terms and conditions',
  }),
});

export const lockCodeSchema = z.object({
  lockCode: z.string().min(4, { message: 'Invalid lock code' }),
});
