import { useState } from 'react';
import { CardType } from '@prisma/client';
import { useRouter } from 'next/navigation';
import { sendOTP, submitPhysicalCardRegistration, submitVirtualCardRegistration } from './actions';
import {
  PhysicalCardInfo,
  VirtualCardInfo,
  PinInfo,
  ContactInfo,
  PhoneOtpInfo,
  RegisterWizardStep,
  LockCodeInfo,
} from './types';
import { formatPhoneNumber } from '@/utils/phone';

export function useRegisterWizard({
  cardType,
  crn = '',
  phone,
}: {
  cardType: CardType;
  crn?: string;
  phone?: string | null;
}) {
  const router = useRouter();
  const [cardInfo, setCardInfo] = useState<PhysicalCardInfo | VirtualCardInfo>({
    crn,
    terms: false,
  });
  const [pinInfo, setPinInfo] = useState<PinInfo>({
    pin: '',
    confirmPin: '',
  });
  const [contactInfo, setContactInfo] = useState<ContactInfo>({
    phone: phone || '',
    firstName: '',
    lastName: '',
  });
  const [lockCode, setLockCode] = useState<LockCodeInfo>({ lockCode: '' });
  const [otp] = useState<PhoneOtpInfo>({ otp: '' });
  const [loading] = useState(false);

  const [step, setStep] = useState<RegisterWizardStep>(RegisterWizardStep.CARD_FORM);

  const handleCardFormSubmit = async (cardInfo: PhysicalCardInfo | VirtualCardInfo) => {
    setCardInfo(cardInfo);
    if (cardType === CardType.PHYSICAL) {
      setStep(RegisterWizardStep.LOCK_CODE_FORM);
    } else {
      toContactInfo();
    }
  };

  const handleLockCodeFormSubmit = (data: LockCodeInfo) => {
    setLockCode(data);
    setStep(RegisterWizardStep.PIN_FORM);
  };

  const handlePinFormSubmit = async (pinInfo: PinInfo) => {
    setPinInfo(pinInfo);
    toContactInfo();
  };

  const handleSendOTP = (phone: string, channel: 'sms' | 'whatsapp') => {
    const phoneNumber = formatPhoneNumber(phone);
    return sendOTP(phoneNumber, channel);
  };

  // If user has a phone number, skip contact and send OTP
  const toContactInfo = async () => {
    if (phone) {
      await handleSendOTP(phone, 'whatsapp');
      setStep(RegisterWizardStep.OTP_FORM);
    } else {
      setStep(RegisterWizardStep.CONTACT_INFO_FORM);
    }
  };

  const handleContactInfoFormSubmit = (contactInfo: ContactInfo) => {
    setContactInfo(contactInfo);
    setStep(RegisterWizardStep.OTP_FORM);

    // First attempt is whatsapp
    handleSendOTP(contactInfo.phone, 'whatsapp');
  };

  const handleResendOTP = () => {
    if (contactInfo?.phone) {
      handleSendOTP(contactInfo.phone, 'sms');
    }
  };

  const handleBack = () => {
    switch (step) {
      case RegisterWizardStep.LOCK_CODE_FORM:
        setStep(RegisterWizardStep.CARD_FORM);
        break;
      case RegisterWizardStep.PIN_FORM:
        setStep(RegisterWizardStep.LOCK_CODE_FORM);
        break;
      case RegisterWizardStep.CONTACT_INFO_FORM:
        setStep(
          cardType === CardType.PHYSICAL
            ? RegisterWizardStep.PIN_FORM
            : RegisterWizardStep.CARD_FORM,
        );
        break;
      case RegisterWizardStep.OTP_FORM:
        setStep(RegisterWizardStep.CONTACT_INFO_FORM);
        break;
    }
  };

  const handleOtpFormSubmit = async () => {
    const { error } = await (cardType === CardType.PHYSICAL
      ? submitPhysicalCardRegistration({
          crn: cardInfo.crn,
          cvv: (cardInfo as PhysicalCardInfo).cvv,
          expiry: (cardInfo as PhysicalCardInfo).expiry,
          pin: pinInfo.pin,
          confirmPin: pinInfo.confirmPin,
          lockCode: lockCode.lockCode,
        })
      : submitVirtualCardRegistration({
          crn: cardInfo.crn,
        }));

    if (error) {
      console.error('Error registering card', error);
      return;
    }
    router.push('/?modal=register-success');
  };

  return {
    cardType,
    cardInfo,
    pinInfo,
    contactInfo,
    otp,
    step,
    loading,
    onBack: handleBack,
    onCardFormSubmit: handleCardFormSubmit,
    onLockFormSubmit: handleLockCodeFormSubmit,
    onPinFormSubmit: handlePinFormSubmit,
    onContactFormSubmit: handleContactInfoFormSubmit,
    onOtpFormSubmit: handleOtpFormSubmit,
    resendOTP: handleResendOTP,
  };
}
