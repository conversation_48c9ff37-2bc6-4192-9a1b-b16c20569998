export enum AuthErrorCode {
  EXPIRED_TOKEN = 'EXPIRED_TOKEN',
  INVALID_TOKEN = 'INVALID_TOKEN',
  FAILED_TO_CREATE_USER = 'failed_to_create_user',
  FAILED_TO_CREATE_SESSION = 'failed_to_create_session',
}

export enum CardRegistrationErrorCode {
  EXPIRED_TOKEN = 'EXPIRED_TOKEN',
  INVALID_TOKEN = 'INVALID_TOKEN',
  INVALID_EMAIL = 'INVALID_EMAIL',
  INVALID_PHYSICAL_CARD = 'INVALID_PHYSICAL_CARD',
  TOKEN_ALREADY_USED = 'TOKEN_ALREADY_USED',
  INVALID_LOCK_CODE = 'INVALID_LOCK_CODE',
  FAILED_TO_REGISTER = 'FAILED_TO_REGISTER',
  FAILED_TO_CREATE_CARD = 'FAILED_TO_CREATE_CARD',
}

export type AppErrorCode = AuthErrorCode | CardRegistrationErrorCode;
