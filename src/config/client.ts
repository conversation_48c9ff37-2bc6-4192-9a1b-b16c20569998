import z from 'zod';

const createClientConfig = () => {
  const ClientEnvSchema = z.object({
    BASE_URL: z.string(),
    POSTMARK_FROM_EMAIL: z.string(),
  });

  // Only include NEXT_PUBLIC_ variables
  const clientEnvVars = {
    BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
    POSTMARK_FROM_EMAIL: process.env.NEXT_PUBLIC_POSTMARK_FROM_EMAIL,
  };

  const parsedEnv = ClientEnvSchema.safeParse(clientEnvVars);

  if (!parsedEnv.success) {
    console.error('Missing client environment variables');
    // Provide fallbacks for client-side to prevent crashes
    return {
      baseUrl: '',
      postmarkFromEmail: '',
    };
  }

  return {
    baseUrl: parsedEnv.data.BASE_URL,
    postmarkFromEmail: parsedEnv.data.POSTMARK_FROM_EMAIL,
  };
};

export default createClientConfig();
