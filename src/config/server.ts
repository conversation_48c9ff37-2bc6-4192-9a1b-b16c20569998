import z from 'zod';

const envSchema = z.object({
  AUTH_SECRET: z.string(),
  BASE_URL: z.string(),
  ENV: z.enum(['local', 'dev', 'staging', 'prod']).default('local'),
  EPAY_PORTAL_API_KEY: z.string(),
  MAGIC_LINK_EXPIRES_IN: z.coerce.number().default(60),
  PIN_BLOCK_KEY: z.string(),
  POSTMARK_API_KEY: z.string(),
  POSTMARK_FROM_EMAIL: z.string(),
  TWILIO_ACCOUNT_SID: z.string(),
  TWILIO_AUTH_TOKEN: z.string(),
  TWILIO_VERIFY_SERVICE_SID: z.string(),
});

type EnvSchema = z.infer<typeof envSchema>;

const createServerConfig = () => {
  const secrets = getSecrets();

  const ENV = process.env.ENV || 'local';

  const envVars = {
    AUTH_SECRET: process.env.AUTH_SECRET,
    BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
    ENV,
    EPAY_PORTAL_API_KEY: process.env.EPAY_PORTAL_API_KEY,
    MAGIC_LINK_EXPIRES_IN: process.env.MAGIC_LINK_EXPIRES_IN,
    POSTMARK_API_KEY: process.env.POSTMARK_API_KEY,
    POSTMARK_FROM_EMAIL: process.env.NEXT_PUBLIC_POSTMARK_FROM_EMAIL,
    TWILIO_ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID,
    TWILIO_AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN,
    TWILIO_VERIFY_SERVICE_SID: process.env.TWILIO_VERIFY_SERVICE_SID,
    PIN_BLOCK_KEY: process.env.PIN_BLOCK_KEY,
    ...secrets,
  };

  const parsed = getParsedEnv(envVars);

  return {
    env: {
      isLocal: parsed.ENV === 'local',
      isDev: parsed.ENV === 'dev',
      isStaging: parsed.ENV === 'staging',
      isProd: parsed.ENV === 'prod',
    },
    auth: {
      baseUrl: parsed.BASE_URL,
      secret: parsed.AUTH_SECRET,
      magicLinkExpiresIn: parsed.MAGIC_LINK_EXPIRES_IN,
    },
    external: {
      epayPortalApiKey: parsed.EPAY_PORTAL_API_KEY,
    },
    pinBlock: {
      key: parsed.PIN_BLOCK_KEY,
    },
    postmark: {
      apiKey: parsed.POSTMARK_API_KEY,
      fromEmail: parsed.POSTMARK_FROM_EMAIL,
    },
    twilio: {
      accountSid: parsed.TWILIO_ACCOUNT_SID,
      authToken: parsed.TWILIO_AUTH_TOKEN,
      verifyServiceSid: parsed.TWILIO_VERIFY_SERVICE_SID,
    },
  };
};

function getParsedEnv(env: EnvSchema): EnvSchema {
  if (env.ENV === 'local') {
    return env;
  }

  const parsedEnv = envSchema.safeParse(env);

  if (!parsedEnv.success || !parsedEnv.data) {
    throw new Error(
      `Invalid env provided.
  The following variables are missing or invalid:
  ${Object.entries(parsedEnv.error.flatten().fieldErrors)
    .map(([k, v]) => `- ${k}: ${v}`)
    .join('\n')}
  `,
    );
  }
  return parsedEnv.data;
}

function getSecrets() {
  try {
    if (process.env.JSON_SECRETS) {
      return JSON.parse(process.env.JSON_SECRETS);
    }
    return {};
  } catch (error) {
    console.error('Failed to parse JSON_SECRETS:', error);
    return {};
  }
}

export default createServerConfig();
