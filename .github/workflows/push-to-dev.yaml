name: deploy-cardholder-portal-DEV
env:
  ENVIRONMENT: dev
  APP_NAME: cardholder-portal
  AWS_ACCOUNT: ************
on:
  push:
    branches:
      - dev

jobs:
  deploy-to-DEV:
    name: 'Deploy to DEV'
    runs-on: ubuntu-latest
    steps:
      - name: 'Checkout'
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@0e613a0980cbf65ed5b322eb7a1e075d28913a83
        with:
          aws-access-key-id: ${{secrets.EPAYNZ_AWS_ACCESS_KEY_DEV}}
          aws-secret-access-key: ${{secrets.EPAYNZ_AWS_SECRET_ACCESS_KEY_DEV}}
          aws-region: ap-southeast-2

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@62f4f872db3836360b72999f4b87f1ff13310f3a

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker buildx create --use
          docker buildx build \
            --build-arg ENV_FILE=.env.${ENVIRONMENT} \
            --load -t ${ECR_REGISTRY}/${ENVIRONMENT}-${APP_NAME}:$IMAGE_TAG .
          docker push ${ECR_REGISTRY}/${ENVIRONMENT}-${APP_NAME}:${IMAGE_TAG}
          sed -i "s|<ECR_REPOSITORY_URL>|$ECR_REGISTRY/${ENVIRONMENT}-${APP_NAME}:${IMAGE_TAG}|g" task-definition.json

      - name: Replace variables task definition
        run: |
          sed -i "s|<ENV>|${ENVIRONMENT}|g" task-definition.json
          sed -i "s|<ENV_SECRET>|${ENVIRONMENT}|g" task-definition.json
          sed -i "s|<AWS_ACCOUNT>|${AWS_ACCOUNT}|g" task-definition.json
          sed -i "s|<TASK_ROLE>|escTaskAdmin|g" task-definition.json
          sed -i "s|<EXECUTION_ROLE>|escTaskAdmin|g" task-definition.json
          sed -i "s|<TASK_DEFINITION_FAMILY>|${ENVIRONMENT}-${APP_NAME}-definition|g" task-definition.json
          sed -i "s|<CONTAINER_NAME>|${ENVIRONMENT}-${APP_NAME}|g" task-definition.json
          sed -i "s|<APP_NAME>|${APP_NAME}|g" task-definition.json
          sed -i "s|<ARN_JSON_SECRETS>|arn:aws:secretsmanager:ap-southeast-2:${AWS_ACCOUNT}:secret:dev/cardholder-portal/JSON_SECRETS-K9Qn8r|g" task-definition.json

      - name: Deploy to Amazon ECS
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: task-definition.json
          service: dev-cardholder-portal
          cluster: dev-epay-cluster
          wait-for-service-stability: true
